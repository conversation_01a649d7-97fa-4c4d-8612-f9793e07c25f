<?xml version="1.0" encoding="UTF-8"?>
<package schemaVersion="1.1" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>GigaDevice</vendor>
  <url>https://gd32mcu.com/data/documents/pack/</url>
  <name>GD32F4xx_DFP</name>
  <description>GigaDevice GD32F4xx Series Device Support and Examples</description>
  <releases>
 <release version="3.0.3" date="2022-07-27">
    Update url address.
  </release>
 <release version="3.0.2" date="2022-06-23">
    Update flash algorithm.
  </release>	
  <release version="3.0.0" date="2022-03-05">
    Add GD32F470, GD32F425 and GD32F427 series to Device Family Pack.
  </release>
  <release version="2.1.0" date="2020-09-08">
    update flash algorithm address range.
  </release>
   <release version="2.0.0" date="2019-03-11">
    Update firmware.
  </release>
  <release version="1.0.4" date="2018-11-11">
    Update firmware.
  </release>
  <release version="1.0.3" date="2017-06-06">
    Update firmware for GD32F403 series.
  </release>
  <release version="1.0.2" date="2017-04-25">
    Add GD32F403 series to Device Family Pack.
  </release>
  <release version="1.0.1" date="2016-11-23">
    Add GD32F405 and GD32F407 series to Device Family Pack.
  </release>
  <release version="1.0.0" date="2016-08-08">
    First Release version of GD32F4 Device Family Pack.
  </release>
  </releases>
  <keywords>
  <!-- keywords for indexing -->
  <keyword>GigaDevice</keyword>
  <keyword>Device Support</keyword>
  <keyword>GD32F4xx</keyword>
  </keywords>
  <devices>
    <family Dfamily="GD32F4xx Series" Dvendor="GigaDevice:123">
      <processor Dcore="Cortex-M4"  Dfpu="1" Dmpu="1" Dendian="Little-endian"/>
      <description>
GD32 is a new 32-bit high performance, low power consumption universal microcontroller family powered by the ARM Cortex-M4 RISC core,which targeted at various MCU application areas.
GD32 family integrates features to simplify system design and provide customers wide range of comprehensive and superior cost effective MCU portfolios with proven technology and great innovation.
GD32 family includes entry line, performance line and connectivity line currently.
      </description>
      <feature type="XTAL"               n="4000000"  m="32000000"  name="Crystal Oscillator"/>
      <feature type="CoreOther"       n="1"                                        name="CRC Calculation Unit"/>
      <feature type="DMA"               n="8"                                         name="General Purpose DMA with Centralized FIFO and Burst Support"/>
      <feature type="IntRC"              n="16000000"                           name="Internal 16M RC oscillator"/>
      <feature type="IntRC"              n="48000000"                           name="Internal 48M RC oscillator"/>      
      <feature type="RTC"                n="32000"                                 name="RTC with 32 kHz calibrated Oscillator and Battery Backup"/>
      <feature type="PLL"                 n="3"                                         name="Internal PLL"/>
      <feature type="PowerMode"    n="4"                                          name="Run, Sleep, Deep-Sleep, Standby"/>
      <feature type="PowerOther"    n="4"                                          name="POR, PDR, LVD, and BPOR"/>
      <feature type="ExtInt"             n="22"                                        name="External interrupt"/>            
      <feature type="Temp"             n="-40"          m="85"                name="Extended Operating Temperature Range"/>
      <feature type="ADC"               n="3"              m="24"                name="High-Performance ADC"/>
      <feature type="TempSens"      n="1"                                          name="Temperature sensor"/>
      <feature type="Timer"             n="2"              m="32"                name="32-bit General Purpose Timer"/>
      <feature type="Timer"             n="8"              m="16"                name="16-bit General Purpose Timer"/>
      <feature type="Timer"             n="2"              m="16"                name="Advanced Timer"/>
      <feature type="Timer"             n="2"              m="16"                name="Basic Timer"/>
      <feature type="Timer"             n="1"              m="24"                name="SysTick Timer"/>
      <feature type="WDT"               n="2"                                          name="Watchdog timer"/>
      <feature type="MPSerial"         n="4"                                          name="Multi-Purpose Serial Interface Module: I2C, I2S, SPI, USART"/> 
      <feature type="USART"            n="4"              m="12500000"    name="High-Speed USART Interface"/>
      <feature type="UART"              n="4"              m="12500000"    name="High-Speed UART Interface"/>
      <feature type="CAN"               n="2"                                          name="CAN 2.0b Controller"/>
      <feature type="USBOTG"          n="2"                                          name="High-Speed/Full-Speed USB OTG with PHY"/>
      <feature type="GLCD"              n="2"              m="2048.2048"   name="TFT LCD Controller"/>
      <feature type="RNG"                n="1"                                          name="True Random Number Generator"/>
      <feature type="SDIO"               n="1"              m="8"                  name="SDIO Interface"/>
      <feature type="Camera"           n="1"              m="14"                name="Digital Camera Interface"/>
      <feature type="IOs"                 n="140"                                       name="IO pins"/>
      <feature type="I2C"                 n="3"                                           name="Low-Power I2C"/>
      <feature type="SPI"                  n="6"                                          name="SPI Interface"/>
      <feature type="I2S"                  n="5"                                          name="I2S Interface"  />
      <feature type="VCC"                n="2.6"                m="3.6"          name="Voltage"/>
      <feature type="NVIC"               n="91"                                        name="NVIC"/>
      <feature type="Other"              n="3"                                         name="My other Interface"/>

      <subFamily DsubFamily="GD32F403">
        <processor Dclock="168000000"/>   
        <description>
        GD32F403 - ARM Cortex-M4 Core
          Frequency up to 168 MHz
          Flash access zero wait state
          Single-cycle multiplier and hardware divider

        Memories
          Flash size from 256KB to 3072KB
          SRAM size is 128 KB with HW parity checking
          6KB ISP loader ROM
        </description>

        <!-- *************************  Device 'GD32F403RC'  ***************************** -->
        <device Dname="GD32F403RC">
          <memory    id="IROM1"                   start="0x08000000" size="0x040000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x0C000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x040000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define=" GD32F403 "/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403RE'  ***************************** -->
        <device Dname="GD32F403RE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x018000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x080000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403RG'  ***************************** -->
        <device Dname="GD32F403RG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0100000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403RI'  ***************************** -->
        <device Dname="GD32F403RI">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0200000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403RK'  ***************************** -->
        <device Dname="GD32F403RK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0300000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403VC'  ***************************** -->
        <device Dname="GD32F403VC">
          <memory    id="IROM1"                   start="0x08000000" size="0x040000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x0C000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x040000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403VE'  ***************************** -->
        <device Dname="GD32F403VE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x018000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x080000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403VG'  ***************************** -->
        <device Dname="GD32F403VG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0100000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define=" GD32F403 "/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403VI'  ***************************** -->
        <device Dname="GD32F403VI">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0200000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define=" GD32F403 "/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403VK'  ***************************** -->
        <device Dname="GD32F403VK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0300000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define=" GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403ZC'  ***************************** -->
        <device Dname="GD32F403ZC">
          <memory    id="IROM1"                   start="0x08000000" size="0x040000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x0C000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x040000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403ZE'  ***************************** -->
        <device Dname="GD32F403ZE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x018000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x080000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403ZG'  ***************************** -->
        <device Dname="GD32F403ZG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0100000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403ZI'  ***************************** -->
        <device Dname="GD32F403ZI">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0200000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>

        <!-- *************************  Device 'GD32F403ZK'  ***************************** -->
        <device Dname="GD32F403ZK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <algorithm name="Flash/GD32F403.FLM" start="0x08000000" size="0x0300000" default="1"/>
          <compile header="Device/F403/Include/gd32f403.h" define="GD32F403"/>
          <debug svd="SVD/GD32F403.svd"/>
        </device>
      </subFamily>
  

<!-- ************************  Subfamily 'GD32F405'  **************************** -->
      <subFamily DsubFamily="GD32F405">
        <processor Dclock="200000000"/>
         <compile header="Device/F4XX/Include/gd32f4xx.h" define="GD32F405"/>
        <debug svd="SVD/GD32F4xx.svd"/>
        <description>
        GD32F405 - ARM Cortex-M4 Core
          Frequency up to 200 MHz
          Flash access zero wait state
          Single-cycle multiplier and hardware divider

        Memories
          Flash size from 512KB to 3072 KB
          SRAM size is 192 KB with HW parity checking
          30KB ISP loader ROM
        </description>

        <!-- *************************  Device 'GD32F405RE'  ***************************** -->
        <device Dname="GD32F405RE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F405RG'  ***************************** -->
        <device Dname="GD32F405RG">
          <memory    id="IROM1"                   start="0x08000000" size="0x100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>
      
        <!-- *************************  Device 'GD32F405RK'  ***************************** -->
        <device Dname="GD32F405RK">
          <memory    id="IROM1"                   start="0x08000000" size="0x300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F405VG'  ***************************** -->
        <device Dname="GD32F405VG">
          <memory    id="IROM1"                   start="0x08000000" size="0x100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>
      
        <!-- *************************  Device 'GD32F405VK'  ***************************** -->
        <device Dname="GD32F405VK">
          <memory    id="IROM1"                   start="0x08000000" size="0x300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F405ZG'  ***************************** -->
        <device Dname="GD32F405ZG">
          <memory    id="IROM1"                   start="0x08000000" size="0x100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>
      
        <!-- *************************  Device 'GD32F405ZK'  ***************************** -->
        <device Dname="GD32F405ZK">
          <memory    id="IROM1"                   start="0x08000000" size="0x300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

      </subFamily>

<!-- ************************  Subfamily 'GD32F407'  **************************** -->
      <subFamily DsubFamily="GD32F407">
        <processor Dclock="200000000"/>
         <compile header="Device/F4XX/Include/gd32f4xx.h" define="GD32F407"/>
        <debug svd="SVD/GD32F4xx.svd"/>
        <description>
        GD32F407 - ARM Cortex-M4 Core
          Frequency up to 200 MHz
          Flash access zero wait state
          Single-cycle multiplier and hardware divider

        Memories
          Flash size from 512KB to 3072 KB
          SRAM size is 192 KB with HW parity checking
          30KB ISP loader ROM
        </description>

       <feature type="ETH"                n="1"              m="100000000"  name="Integrated Ethernet MAC with PHY"/>

        <!-- *************************  Device 'GD32F407RE'  ***************************** -->
        <device Dname="GD32F407RE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F407RG'  ***************************** -->
        <device Dname="GD32F407RG">
          <memory    id="IROM1"                   start="0x08000000" size="0x100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>
      
        <!-- *************************  Device 'GD32F407RK'  ***************************** -->
        <device Dname="GD32F407RK">
          <memory    id="IROM1"                   start="0x08000000" size="0x300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

      <!-- *************************  Device 'GD32F407VE'  ***************************** -->
        <device Dname="GD32F407VE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F407VG'  ***************************** -->
        <device Dname="GD32F407VG">
          <memory    id="IROM1"                   start="0x08000000" size="0x100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>
      
        <!-- *************************  Device 'GD32F407VK'  ***************************** -->
        <device Dname="GD32F407VK">
          <memory    id="IROM1"                   start="0x08000000" size="0x300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

      <!-- *************************  Device 'GD32F407ZE'  ***************************** -->
        <device Dname="GD32F407ZE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F407ZG'  ***************************** -->
        <device Dname="GD32F407ZG">
          <memory    id="IROM1"                   start="0x08000000" size="0x100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>
      
        <!-- *************************  Device 'GD32F407ZK'  ***************************** -->
        <device Dname="GD32F407ZK">
          <memory    id="IROM1"                   start="0x08000000" size="0x300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F407IE'  ***************************** -->
        <device Dname="GD32F407IE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F407IG'  ***************************** -->
        <device Dname="GD32F407IG">
          <memory    id="IROM1"                   start="0x08000000" size="0x100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>
      
        <!-- *************************  Device 'GD32F407IK'  ***************************** -->
        <device Dname="GD32F407IK">
          <memory    id="IROM1"                   start="0x08000000" size="0x300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x020000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

      </subFamily>

      <!-- ************************  Subfamily 'GD32F450'  **************************** -->
      <subFamily DsubFamily="GD32F450">
        <processor Dclock="200000000"/>
         <compile header="Device/F4XX/Include/gd32f4xx.h" define="GD32F450"/>
        <debug svd="SVD/GD32F4xx.svd"/>
        <description>
        GD32F450 - ARM Cortex-M4 Core
          Frequency up to 200 MHz
          Flash access zero wait state
          Single-cycle multiplier and hardware divider

        Memories
          Flash size from 512KB to 3072 KB
          SRAM size is 256 KB with HW parity checking
          30KB ISP loader ROM
        </description>

        <feature type="ETH"                n="1"              m="100000000"  name="Integrated Ethernet MAC with PHY"/>

        <!-- *************************  Device 'GD32F450VE'  ***************************** -->
        <device Dname="GD32F450VE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450VG'  ***************************** -->
        <device Dname="GD32F450VG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450VI'  ***************************** -->
        <device Dname="GD32F450VI">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x070000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_2MB.FLM" start="0x08000000" size="0x0200000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450VK'  ***************************** -->
        <device Dname="GD32F450VK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450ZE'  ***************************** -->
        <device Dname="GD32F450ZE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450ZG'  ***************************** -->
        <device Dname="GD32F450ZG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450ZI'  ***************************** -->
        <device Dname="GD32F450ZI">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x070000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_2MB.FLM" start="0x08000000" size="0x0200000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450ZK'  ***************************** -->
        <device Dname="GD32F450ZK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450IG'  ***************************** -->
        <device Dname="GD32F450IG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

       <!-- *************************  Device 'GD32F450II'  ***************************** -->
        <device Dname="GD32F450II">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x070000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_2MB.FLM" start="0x08000000" size="0x0200000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F450IK'  ***************************** -->
        <device Dname="GD32F450IK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'GD32F470'  **************************** -->
      <subFamily DsubFamily="GD32F470">
        <processor Dclock="240000000"/>
         <compile header="Device/F4XX/Include/gd32f4xx.h" define="GD32F470"/>
        <debug svd="SVD/GD32F4xx.svd"/>
        <description>
        GD32F470 - ARM Cortex-M4 Core
          Frequency up to 240 MHz
          Flash access zero wait state
          Single-cycle multiplier and hardware divider

        Memories
          Flash size from 512KB to 3072KB
          SRAM size from 256KB to  768KB with HW parity checking
          30KB ISP loader ROM
        </description>

        <feature type="ETH"                n="1"              m="100000000"  name="Integrated Ethernet MAC with PHY"/>

        <!-- *************************  Device 'GD32F470VE'  ***************************** -->
        <device Dname="GD32F470VE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470VG'  ***************************** -->
        <device Dname="GD32F470VG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x070000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470VI'  ***************************** -->
        <device Dname="GD32F470VI">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x0B0000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_2MB.FLM" start="0x08000000" size="0x0200000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470VK'  ***************************** -->
        <device Dname="GD32F470VK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470ZE'  ***************************** -->
        <device Dname="GD32F470ZE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470ZG'  ***************************** -->
        <device Dname="GD32F470ZG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x070000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470ZI'  ***************************** -->
        <device Dname="GD32F470ZI">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x0B0000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_2MB.FLM" start="0x08000000" size="0x0200000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470ZK'  ***************************** -->
        <device Dname="GD32F470ZK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470IG'  ***************************** -->
        <device Dname="GD32F470IG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

       <!-- *************************  Device 'GD32F470II'  ***************************** -->
        <device Dname="GD32F470II">
          <memory    id="IROM1"                   start="0x08000000" size="0x0200000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x070000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_2MB.FLM" start="0x08000000" size="0x0200000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F470IK'  ***************************** -->
        <device Dname="GD32F470IK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'GD32F425'  **************************** -->
      <subFamily DsubFamily="GD32F425">
        <processor Dclock="200000000"/>
         <compile header="Device/F4XX/Include/gd32f4xx.h" define="GD32F425"/>
        <debug svd="SVD/GD32F4xx.svd"/>
        <description>
        GD32F425 - ARM Cortex-M4 Core
          Frequency up to 200 MHz
          Flash access zero wait state
          Single-cycle multiplier and hardware divider

        Memories
          Flash size from 512KB to 3072KB
          SRAM size is 256KB with HW parity checking
          30KB ISP loader ROM
        </description>

        <feature type="ETH"                n="1"              m="100000000"  name="Integrated Ethernet MAC with PHY"/>

        <!-- *************************  Device 'GD32F425RE'  ***************************** -->
        <device Dname="GD32F425RE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F425RG'  ***************************** -->
        <device Dname="GD32F425RG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F425RK'  ***************************** -->
        <device Dname="GD32F425RK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F425VG'  ***************************** -->
        <device Dname="GD32F425VG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F425VK'  ***************************** -->
        <device Dname="GD32F425VK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F425ZG'  ***************************** -->
        <device Dname="GD32F425ZG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F425ZK'  ***************************** -->
        <device Dname="GD32F425ZK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'GD32F427'  **************************** -->
      <subFamily DsubFamily="GD32F427">
        <processor Dclock="200000000"/>
         <compile header="Device/F4XX/Include/gd32f4xx.h" define="GD32F427"/>
        <debug svd="SVD/GD32F4xx.svd"/>
        <description>
        GD32F427 - ARM Cortex-M4 Core
          Frequency up to 200 MHz
          Flash access zero wait state
          Single-cycle multiplier and hardware divider

        Memories
          Flash size from 512KB to 3072KB
          SRAM size from 256KB to  768KB with HW parity checking
          30KB ISP loader ROM
        </description>

        <feature type="ETH"                n="1"              m="100000000"  name="Integrated Ethernet MAC with PHY"/>

        <!-- *************************  Device 'GD32F427RE'  ***************************** -->
        <device Dname="GD32F427RE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427RG'  ***************************** -->
        <device Dname="GD32F427RG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427RK'  ***************************** -->
        <device Dname="GD32F427RK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>


        <!-- *************************  Device 'GD32F427VE'  ***************************** -->
        <device Dname="GD32F427VE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427VG'  ***************************** -->
        <device Dname="GD32F427VG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427VK'  ***************************** -->
        <device Dname="GD32F427VK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427ZE'  ***************************** -->
        <device Dname="GD32F427ZE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427ZG'  ***************************** -->
        <device Dname="GD32F427ZG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427ZK'  ***************************** -->
        <device Dname="GD32F427ZK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427IE'  ***************************** -->
        <device Dname="GD32F427IE">
          <memory    id="IROM1"                   start="0x08000000" size="0x080000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_512KB.FLM" start="0x08000000" size="0x080000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427IG'  ***************************** -->
        <device Dname="GD32F427IG">
          <memory    id="IROM1"                   start="0x08000000" size="0x0100000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_1MB.FLM" start="0x08000000" size="0x0100000" default="1"/>
        </device>

        <!-- *************************  Device 'GD32F427IK'  ***************************** -->
        <device Dname="GD32F427IK">
          <memory    id="IROM1"                   start="0x08000000" size="0x0300000" startup="1" default="1"/>
          <memory    id="IRAM1"                   start="0x20000000" size="0x030000" init   ="0" default="1"/>
          <memory    id="IRAM2"                   start="0x10000000" size="0x010000" init   ="0" default="0"/>
          <algorithm name="Flash/GD32F4xx_3MB.FLM" start="0x08000000" size="0x0300000" default="1"/>
        </device>
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- Compiler Conditions -->
    <condition id="Compiler ARMCC">
      <require Tcompiler="ARMCC"/>
    </condition>

   <!-- Device Conditions -->
    <condition id="GD32F403">
      <description>GigaDevice GD32F403xx Devices</description>
      <require Dvendor="GigaDevice:123" Dname="GD32F403*"/>
      <require Cclass="Device" Cgroup="Startup" />
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="GD32F403 CMSIS">
      <description>GigaDevice GD32F403 Devices and CMSIS-CORE</description>
      <require condition="GD32F403"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="Device" Cgroup="Libopt" />    
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="RCU"/>           
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="PMU"/>     
    </condition>

    <!-- GD32F403 STDPERIPHERALS RCU Conditions -->
    <condition id="GD32F403 403_STDPERIPHERALS RCU">
      <description>GigaDevice  GD32F403 Standard Peripherals Drivers with RCU</description>
      <require condition="GD32F403 CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="RCU"/>
    </condition>

     <!-- GD32F403 STDPERIPHERALS MISC Conditions -->
    <condition id="GD32F403 403_STDPERIPHERALS MISC">
      <description>GigaDevice  GD32F403 Standard Peripherals Drivers with MISC</description>
      <require condition="GD32F403 CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="MISC"/>
    </condition>

     <!-- GD32F403 STDPERIPHERALS EVAL Conditions -->
    <condition id="GD32F403 STDPERIPHERALS EVAL">
      <description>GigaDevice  GD32F403 Standard Peripherals Drivers with EVAL</description>
      <require condition="GD32F403 CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="GPIO"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="EXTI"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="USART"/>
    </condition>

   <!-- Device Conditions -->
    <condition id="GD32F405">
      <description>GigaDevice GD32F405 Devices</description>
      <require Dvendor="GigaDevice:123" Dname="GD32F405*"/>
      <require Cclass="Device" Cgroup="Startup" />
    </condition>

    <!-- Device Conditions -->
    <condition id="GD32F407">
      <description>GigaDevice GD32F407 Devices</description>
      <require Dvendor="GigaDevice:123" Dname="GD32F407*"/>
      <require Cclass="Device" Cgroup="Startup" />
    </condition>

    <!-- Device Conditions -->
    <condition id="GD32F450">
      <description>GigaDevice GD32F450 Devices</description>
      <require Dvendor="GigaDevice:123" Dname="GD32F450*"/>
      <require Cclass="Device" Cgroup="Startup" />     
    </condition>

    <!-- Device Conditions -->
    <condition id="GD32F470">
      <description>GigaDevice GD32F470 Devices</description>
      <require Dvendor="GigaDevice:123" Dname="GD32F470*"/>
      <require Cclass="Device" Cgroup="Startup" />     
    </condition>

    <!-- Device Conditions -->
    <condition id="GD32F425">
      <description>GigaDevice GD32F425 Devices</description>
      <require Dvendor="GigaDevice:123" Dname="GD32F425*"/>
      <require Cclass="Device" Cgroup="Startup" />     
    </condition>

    <!-- Device Conditions -->
    <condition id="GD32F427">
      <description>GigaDevice GD32F427 Devices</description>
      <require Dvendor="GigaDevice:123" Dname="GD32F427*"/>
      <require Cclass="Device" Cgroup="Startup" />     
    </condition>

    <!-- Device + CMSIS Conditions -->
    <condition id="GD32F4xx CMSIS">
      <description>GigaDevice GD32F4xx Devices and CMSIS-CORE</description>
      <accept condition="GD32F405"/>
      <accept condition="GD32F407"/>
      <accept condition="GD32F450"/>
      <accept condition="GD32F470"/>
      <accept condition="GD32F425"/>
      <accept condition="GD32F427"/>
      <accept  Cclass="Device" Cgroup="Libopt"  />
      <require Cclass="CMSIS" Cgroup="CORE"/>   
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="RCU"/>    
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="PMU"/>        
    </condition>

    <!-- GD32F4xx STDPERIPHERALS RCU Conditions -->
    <condition id="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>GigaDevice  GD32F4xx Standard Peripherals Drivers with RCU</description>
      <require condition="GD32F4xx CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="RCU"/>
    </condition>

     <!-- GD32F4xx STDPERIPHERALS MISC Conditions -->
    <condition id="GD32F4xx 4XX_STDPERIPHERALS MISC">
      <description>GigaDevice  GD32F4xx Standard Peripherals Drivers with MISC</description>
      <require condition="GD32F4xx CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="MISC"/>
    </condition>

     <!-- GD32F4xx STDPERIPHERALS EVAL Conditions -->
    <condition id="GD32F4xx STDPERIPHERALS EVAL">
      <description>GigaDevice  GD32F4xx Standard Peripherals Drivers with EVAL</description>
      <require condition="GD32F4xx CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="GPIO"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="EXTI"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="USART"/>
      <require Cclass="Device" Cgroup="StdPeripherals" Csub="SYSCFG"/>
    </condition>
  </conditions>

  <components>
    <!-- GD32F4xx_StdPeripherals -->
    <component Cclass="Device" Cgroup="StdPeripherals" Csub="ADC" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Analog-to-digital converter (ADC) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_ADC
        </RTE_Components_h>
        <files>
          <!-- ADC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_adc.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_adc.c" attr="config" version="3.0.2" />
        </files>
    </component>

    <component Cclass="Device" Cgroup="StdPeripherals" Csub="CAN" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Controller Area Network (CAN) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_CAN
        </RTE_Components_h>
        <files>
          <!-- CAN flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_can.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_can.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="CRC" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Cyclic Redundancy Check (CRC) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_CRC
        </RTE_Components_h>
        <files>
          <!-- CRC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_crc.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_crc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="CTC" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Clock trim controller (CTC) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_CTC
        </RTE_Components_h>
        <files>
          <!-- CTC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_ctc.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_ctc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="DAC" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Digital-to-analog converter (DAC) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_DAC
        </RTE_Components_h>
        <files>
          <!-- DAC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_dac.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_dac.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="DBG" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Debug (DBG) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_DBG
        </RTE_Components_h>
        <files>
          <!-- DBG flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_dbg.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_dbg.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="DCI" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Digital camera interface (DCI) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_DCI
        </RTE_Components_h>
        <files>
          <!-- DCI flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_dci.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_dci.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="DMA" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Direct Memory Access (DMA) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_DMA
        </RTE_Components_h>
        <files>
          <!-- DMA flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_dma.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_dma.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="ENET" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Ethernet(ENET) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_ENET
        </RTE_Components_h>
        <files>
          <!-- ENET flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_enet.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_enet.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="EXMC" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>External memory controller(EXMC) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_EXMC
        </RTE_Components_h>
        <files>
          <!-- EXMC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_exmc.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_exmc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="EXTI" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>External Interrupt/Event (EXTI) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_EXTI
        </RTE_Components_h>
        <files>
          <!-- EXTI flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_exti.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_exti.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="FMC" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Flash Memory Controller (FMC) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_FMC
        </RTE_Components_h>
        <files>
          <!-- FMC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_fmc.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_fmc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="FWDGT" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Free watchdog timer(FWDGT) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_FWDGT
        </RTE_Components_h>
        <files>
          <!-- FWDGT flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_fwdgt.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_fwdgt.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="GPIO" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>General-purpose and Alternate-function I/Os (GPIO) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_GPIO
        </RTE_Components_h>
        <files>
          <!-- GPIO flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_gpio.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_gpio.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="I2C" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Inter-integrated Circuit (I2C) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_I2C
        </RTE_Components_h>
        <files>
          <!-- I2C flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_i2c.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_i2c.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="IPA" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Image processing accelerator (IPA) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_IPA
        </RTE_Components_h>
        <files>
          <!-- IPA flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_ipa.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_ipa.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="IREF" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Programmable current reference(IREF) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_IREF
        </RTE_Components_h>
        <files>
          <!-- IREF flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_iref.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_iref.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="MISC" Cversion="3.0.2" condition="GD32F4xx CMSIS">
      <description>MISC driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_MISC
        </RTE_Components_h>
        <files>
          <!-- MISC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_misc.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_misc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="PMU" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Power Managment Unit(PMU) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_PMU
        </RTE_Components_h>
        <files>
          <!-- PMU flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_pmu.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_pmu.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="RCU" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS MISC">
      <description>Reset and Clock Control (RCU) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_RCU
        </RTE_Components_h>
        <files>
          <!-- RCU flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_rcu.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_rcu.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="RTC" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Real-time Clock (RTC) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_RTC
        </RTE_Components_h>
        <files>
          <!-- RTC flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_rtc.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_rtc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="SDIO" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Secure digital input/output interface(SDIO) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_SDIO
        </RTE_Components_h>
        <files>
          <!-- SDIO flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_sdio.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_sdio.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="SPI_I2S" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Serial Peripheral Interface / Inter-IC Sound (SPI_I2S) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_SPI
        </RTE_Components_h>
        <files>
          <!-- SPI flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_spi.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_spi.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="SYSCFG" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>System Configuration(SYSCFG) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_SYSCFG
        </RTE_Components_h>
        <files>
          <!-- SYSCFG flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_syscfg.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_syscfg.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="TIMER" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>TIMER driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_TIMER
        </RTE_Components_h>
        <files>
          <!-- TIMER flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_timer.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_timer.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="TLI" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>TFT-LCD interface(TLI) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_TLI
        </RTE_Components_h>
        <files>
          <!-- TLI flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_tli.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_tli.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="TRNG" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>True random number generator (TRNG) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_TRNG
        </RTE_Components_h>
        <files>
          <!-- TRNG flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_trng.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_trng.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="USART" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Universal Synchronous Asynchronous Receiver Transmitter (USART) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_USART
        </RTE_Components_h>
        <files>
          <!-- USART flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_usart.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_usart.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="WWDGT" Cversion="3.0.2" condition="GD32F4xx 4XX_STDPERIPHERALS RCU">
      <description>Window Watchdog Timer (WWDGT) driver for GD32F4xx Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_WWDGT
        </RTE_Components_h>
        <files>
          <!-- WWDGT flie -->
          <file category="header" name="Device/F4XX/Firmware/Peripherals/inc/gd32f4xx_wwdgt.h" />
          <file category="source" name="Device/F4XX/Firmware/Peripherals/src/gd32f4xx_wwdgt.c" attr="config" version="3.0.2" />
        </files>
    </component>

    <!-- Utilities GD32F4xx -->

   <component Cclass="Device" Cgroup="EVAL" Csub="GD32F450I" Cversion="3.0.2" condition="GD32F4xx STDPERIPHERALS EVAL">
      <description>Firmware functions to manage Leds, Keys, COM ports</description>
      <files>
        <!-- include folder -->
        <file category="header" name="Device/F4XX/Utilities/gd32f450i_eval.h"/>
        <!-- eval file -->
        <file category="source" name="Device/F4XX/Utilities/gd32f450i_eval.c" attr="config" version="3.0.2"/>
      </files>
    </component>

    <component Cclass="Device" Cgroup="EVAL" Csub="GD32F450Z" Cversion="3.0.2" condition="GD32F4xx STDPERIPHERALS EVAL">
      <description>Firmware functions to manage Leds, Keys, COM ports</description>
      <files>
        <!-- include folder -->
        <file category="header" name="Device/F4XX/Utilities/gd32f450z_eval.h"/>
        <!-- eval file -->
        <file category="source" name="Device/F4XX/Utilities/gd32f450z_eval.c" attr="config" version="3.0.2"/>
      </files>
    </component>

    <!-- Config GD32F4xx -->
    <component Cclass="Device" Cgroup="Libopt"  Cversion="3.0.2" condition="GD32F4xx CMSIS">
      <description> GD32F405/407/450 Series Configuration file</description>
      <files>
        <!-- include folder -->
        <file category="header" name="Device/F4XX/Include/gd32f4xx_libopt.h"/>
      </files>
    </component>


   <!-- Startup GD32F405 -->
    <component Cclass="Device" Cgroup="Startup"  Cversion="3.0.2" condition="GD32F405">
      <description>System Startup for GigaDevice GD32F405 Devices</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/F4XX/Include/"/>
        <file category="header" name="Device/F4XX/Include/gd32f4xx.h"/>
        <!-- startup file -->
        <file category="source" name="Device/F4XX/Source/ARM/startup_gd32f405_425.s" attr="config" version="3.0.2" condition="Compiler ARMCC"/>
        <!-- system file -->
        <file category="source" name="Device/F4XX/Source/system_gd32f4xx.c"      attr="config" version="3.0.2"/>
      </files>
    </component>

   <!-- Startup GD32F407 -->
    <component Cclass="Device" Cgroup="Startup"   Cversion="3.0.2" condition="GD32F407">
      <description>System Startup for GigaDevice GD32F407 Devices</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/F4XX/Include/"/>
        <file category="header" name="Device/F4XX/Include/gd32f4xx.h"/>
        <!-- startup file -->
        <file category="source" name="Device/F4XX/Source/ARM/startup_gd32f407_427.s" attr="config" version="3.0.2" condition="Compiler ARMCC"/>
        <!-- system file -->
        <file category="source" name="Device/F4XX/Source/system_gd32f4xx.c"      attr="config" version="3.0.2"/>

      </files>
    </component>

   <!-- Startup GD32F450 -->
    <component Cclass="Device" Cgroup="Startup"   Cversion="3.0.2" condition="GD32F450">
      <description>System Startup for GigaDevice GD32F450 Devices</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/F4XX/Include/"/>
        <file category="header" name="Device/F4XX/Include/gd32f4xx.h"/>
        <!-- startup file -->
        <file category="source" name="Device/F4XX/Source/ARM/startup_gd32f450_470.s" attr="config" version="3.0.2" condition="Compiler ARMCC"/>
        <!-- system file -->
        <file category="source" name="Device/F4XX/Source/system_gd32f4xx.c"      attr="config" version="3.0.2"/>
      </files>
    </component>
 
    <!-- GD32F403_StdPeripherals -->
    <component Cclass="Device" Cgroup="StdPeripherals" Csub="ADC" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Analog-to-digital converter (ADC) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_ADC
        </RTE_Components_h>
        <files>
          <!-- ADC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_adc.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_adc.c" attr="config" version="3.0.2" />
        </files>
    </component>

   <component Cclass="Device" Cgroup="StdPeripherals" Csub="BKP" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Backup register (BKP) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_BKP
        </RTE_Components_h>
        <files>
          <!-- BKP flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_bkp.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_bkp.c" attr="config" version="3.0.2" />
        </files>
    </component>

    <component Cclass="Device" Cgroup="StdPeripherals" Csub="CAN" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Controller Area Network (CAN) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_CAN
        </RTE_Components_h>
        <files>
          <!-- CAN flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_can.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_can.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="CRC" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Cyclic Redundancy Check (CRC) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_CRC
        </RTE_Components_h>
        <files>
          <!-- CRC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_crc.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_crc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="CTC" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Clock trim controller (CTC) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_CTC
        </RTE_Components_h>
        <files>
          <!-- CTC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_ctc.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_ctc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="DAC" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Digital-to-analog converter (DAC) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_DAC
        </RTE_Components_h>
        <files>
          <!-- DAC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_dac.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_dac.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="DBG" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Debug (DBG) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_DBG
        </RTE_Components_h>
        <files>
          <!-- DBG flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_dbg.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_dbg.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="DMA" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Direct Memory Access (DMA) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_DMA
        </RTE_Components_h>
        <files>
          <!-- DMA flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_dma.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_dma.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="EXMC" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>External memory controller(EXMC) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_EXMC
        </RTE_Components_h>
        <files>
          <!-- EXMC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_exmc.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_exmc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="EXTI" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>External Interrupt/Event (EXTI) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_EXTI
        </RTE_Components_h>
        <files>
          <!-- EXTI flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_exti.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_exti.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="FMC" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Flash Memory Controller (FMC) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_FMC
        </RTE_Components_h>
        <files>
          <!-- FMC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_fmc.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_fmc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="FWDGT" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Free watchdog timer(FWDGT) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_FWDGT
        </RTE_Components_h>
        <files>
          <!-- FWDGT flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_fwdgt.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_fwdgt.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="GPIO" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>General-purpose and Alternate-function I/Os (GPIO) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_GPIO
        </RTE_Components_h>
        <files>
          <!-- GPIO flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_gpio.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_gpio.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="I2C" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Inter-integrated Circuit (I2C) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_I2C
        </RTE_Components_h>
        <files>
          <!-- I2C flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_i2c.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_i2c.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="MISC" Cversion="3.0.2" condition="GD32F403 CMSIS">
      <description>MISC driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_MISC
        </RTE_Components_h>
        <files>
          <!-- MISC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_misc.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_misc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="PMU" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Power Managment Unit(PMU) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_PMU
        </RTE_Components_h>
        <files>
          <!-- PMU flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_pmu.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_pmu.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="RCU" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS MISC">
      <description>Reset and Clock Control (RCU) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_RCU
        </RTE_Components_h>
        <files>
          <!-- RCU flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_rcu.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_rcu.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="RTC" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Real-time Clock (RTC) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_RTC
        </RTE_Components_h>
        <files>
          <!-- RTC flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_rtc.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_rtc.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="SDIO" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Secure digital input/output interface(SDIO) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_SDIO
        </RTE_Components_h>
        <files>
          <!-- SDIO flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_sdio.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_sdio.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="SPI_I2S" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Serial Peripheral Interface / Inter-IC Sound (SPI_I2S) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_SPI
        </RTE_Components_h>
        <files>
          <!-- SPI flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_spi.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_spi.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="TIMER" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>TIMER driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_TIMER
        </RTE_Components_h>
        <files>
          <!-- TIMER flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_timer.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_timer.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="USART" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Universal Synchronous Asynchronous Receiver Transmitter (USART) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_USART
        </RTE_Components_h>
        <files>
          <!-- USART flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_usart.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_usart.c" attr="config" version="3.0.2" />
        </files>
    </component>

  <component Cclass="Device" Cgroup="StdPeripherals" Csub="WWDGT" Cversion="3.0.2" condition="GD32F403 403_STDPERIPHERALS RCU">
      <description>Window Watchdog Timer (WWDGT) driver for GD32F403 Devices</description>
        <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPHERALS_WWDGT
        </RTE_Components_h>
        <files>
          <!-- WWDGT flie -->
          <file category="header" name="Device/F403/Firmware/Peripherals/inc/gd32f403_wwdgt.h" />
          <file category="source" name="Device/F403/Firmware/Peripherals/src/gd32f403_wwdgt.c" attr="config" version="3.0.2" />
        </files>
    </component>

    <!-- Utilities GD32F403 -->
    <component Cclass="Device" Cgroup="EVAL" Csub="GD32F403" Cversion="3.0.2" condition="GD32F403 STDPERIPHERALS EVAL">
      <description>Firmware functions to manage Leds, Keys, COM ports</description>
      <files>
        <!-- include folder -->
        <file category="header" name="Device/F403/Utilities/gd32f403z_eval.h"/>
        <!-- eval file -->
        <file category="source" name="Device/F403/Utilities/gd32f403z_eval.c" attr="config" version="3.0.2"/>
      </files>
    </component>

    <!-- Config GD32F403 -->
    <component Cclass="Device" Cgroup="Libopt"   Cversion="3.0.2" condition="GD32F403 CMSIS">
      <description>GD32F403 Series Configuration file</description>
      <files>
        <!-- include folder -->
        <file category="header" name="Device/F403/Include/gd32f403_libopt.h"/>
      </files>
    </component>

   <!-- Startup GD32F403 -->
    <component Cclass="Device" Cgroup="Startup"  Cversion="3.0.2" condition="GD32F403">
      <description>System Startup for GigaDevice GD32F403 Devices</description>
      <files>
        <!-- include folder -->
        <file category="include" name="Device/F403/Include/"/>
        <file category="header" name="Device/F403/Include/gd32f403.h"/>
        <!-- startup file -->
        <file category="source" name="Device/F403/Source/ARM/startup_gd32f403.s" attr="config" version="3.0.2" condition="Compiler ARMCC"/>
        <!-- system file -->
        <file category="source" name="Device/F403/Source/system_gd32f403.c"      attr="config" version="3.0.2"/>
      </files>
    </component>

</components>
</package>
